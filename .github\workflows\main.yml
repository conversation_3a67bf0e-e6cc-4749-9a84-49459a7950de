name: Sync to Hugging Face Space
on:
  push:
    branches: [main]

  # to run this workflow manually from the Actions tab 
  workflow_dispatch:

jobs:
  sync-to-hub:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          lfs: false
          
      - name: Ignore large files
        run : git filter-branch --index-filter 'git rm -rf --cached --ignore-unmatch "Rag_Documents/layout-parser-paper.pdf"' HEAD

      - name: Push to hub
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        run: git push --force https://ChiragPanchal020:$<EMAIL>/spaces/ChiragPanchal020/Agentic_AI main
       
         