FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install required system packages
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy files
COPY requirements.txt ./
COPY .streamlit/ ./.streamlit/
COPY src/ ./src/

# Install Python dependencies
RUN pip install -r requirements.txt

# Expose port
EXPOSE 8501

# Run the app
CMD ["streamlit", "run", "src/streamlit_app.py", "--server.port=8501", "--server.address=0.0.0.0"]

